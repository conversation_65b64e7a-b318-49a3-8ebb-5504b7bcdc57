<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use tool_lfxp\admin\admin_setting_configcheckbox_with_callback;

/**
 * TODO describe file settings
 *
 * @package    local_offermanager
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    $settings = new admin_settingpage('local_offermanager', new lang_string('pluginname', 'local_offermanager'));

    $ADMIN->add('localplugins', $settings);

    $settings->add(new admin_setting_heading(
        'title',
        get_string('settings'),
        ''
    ));

    $settings->add(new admin_setting_configcheckbox_with_callback(
        'local_offermanager/enableplugin',
        get_string('config:enableplugin', 'local_offermanager'),
        get_string('config:enableplugin_desc', 'local_offermanager'),
        0,
        [\local_offermanager\enrol_setup::class, 'handle_disable_plugins'] 
    ));

    $settings->add(new admin_setting_configcheckbox_with_callback(
        'local_offermanager/enableplatformenrol',
        get_string('config:enableplatformenrol', 'local_offermanager'),
        get_string('config:enableplatformenrol_desc', 'local_offermanager'),
        1,
        [\local_offermanager\enrol_setup::class, 'handle_enrol_plugins'] 
    ));

    $settings->add(new admin_setting_heading(
        'type__menu',
        get_string('config:optionsmenu', 'local_offermanager'),
        ''
    ));

    $settings->add(new admin_setting_configcheckbox(
        'local_offermanager/enabletypeoptions',
        get_string('config:enabletypeoptions', 'local_offermanager'),
        get_string('config:typeoptions_help', 'local_offermanager'),
        0
    ));

    $settings->add(new admin_setting_configtextarea(
        'local_offermanager/typeoptions',
        get_string('config:typeoptions', 'local_offermanager'),
        get_string('config:typeoptions_desc', 'local_offermanager'),
        "online\npresencial"
    ));

    $settings->add(new admin_setting_configtext(
        'local_offermanager/defaulttypeoption',
        get_string('config:defaulttypeoption', 'local_offermanager'),
        get_string('config:defaulttypeoption_desc', 'local_offermanager'),
        'online'
    ));
}
