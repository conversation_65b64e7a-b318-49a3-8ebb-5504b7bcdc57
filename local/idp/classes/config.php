<?php namespace local_idp;

require_once($CFG->dirroot . '/local/idp/vendor/autoload.php');

use lang_string;
use LightSaml\Credential\X509Certificate;
use LightSaml\Credential\KeyHelper;
use Rob<PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityKey;

class config {
    const PLUGIN_COMPONENT = 'local_idp';

    const SETTING_SAML2_SP_METADATA = 'saml2_sp_metadata';

    public static function get_setting_name(string $name) : string {
        return config::PLUGIN_COMPONENT . "/$name";
    }

    public static function get_setting_title_lang_string(string $name) : lang_string {
        return new lang_string("settings:{$name}", self::PLUGIN_COMPONENT);
    }

    public static function get_setting_description_lang_string(string $name) : lang_string {
        return new lang_string("settings:{$name}_desc", self::PLUGIN_COMPONENT);
    }

    /**
     * Return a plugin config by key
     *
     * @param string $key
     * @return mixed|null
     */
    public static function get($key, $default = null){
        try {
            return get_config(self::PLUGIN_COMPONENT, $key) ?: $default;
        } catch (\Throwable $th) {
            return $default;
        }
    }

    /**
     * Sets a value to a config
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set(string $key, $value){
        set_config($key, $value, self::PLUGIN_COMPONENT);
    }












    
    protected static function get_saml2_certificates_directory() : string {
        global $CFG;
        return $CFG->dataroot . '/saml2';
    }

    public static function get_idp_certificate(): X509Certificate {
        $path = self::get_saml2_certificates_directory();
        return X509Certificate::fromFile("$path/saml.crt");
    }

    public static function get_idp_private_key(): XMLSecurityKey {
        $path = self::get_saml2_certificates_directory();

        return KeyHelper::createPrivateKey(
            "$path/saml.pem",
            '',
            true,
            XMLSecurityKey::RSA_SHA256
        );
    }

    public static function get_saml2_idp_entity_id() : string {
        return (new \moodle_url('/local/idp/saml2/metadata.php'))->out(false);
    }

    public static function get_saml2_sso_url() : string {
        return (new \moodle_url('/local/idp/saml2/sso.php'))->out(false);
    }

    public static function get_saml2_sp_metadata() : string {
        return trim(self::get(self::SETTING_SAML2_SP_METADATA, ''));
    }
}