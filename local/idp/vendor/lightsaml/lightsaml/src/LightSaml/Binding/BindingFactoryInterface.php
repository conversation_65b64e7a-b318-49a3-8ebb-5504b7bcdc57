<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Binding;

use Symfony\Component\HttpFoundation\Request;

interface BindingFactoryInterface
{
    /**
     * @return AbstractBinding
     */
    public function getBindingByRequest(Request $request);

    /**
     * @param string $bindingType
     *
     * @throws \LightSaml\Error\LightSamlBindingException
     *
     * @return AbstractBinding
     */
    public function create($bindingType);

    /**
     * @return string|null
     */
    public function detectBindingType(Request $request);
}
