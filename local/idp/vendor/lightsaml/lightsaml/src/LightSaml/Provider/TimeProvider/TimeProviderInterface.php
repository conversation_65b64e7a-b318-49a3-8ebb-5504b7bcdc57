<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Provider\TimeProvider;

interface TimeProviderInterface
{
    /**
     * @return int
     */
    public function getTimestamp();

    /**
     * @return \DateTime
     */
    public function getDateTime();
}
