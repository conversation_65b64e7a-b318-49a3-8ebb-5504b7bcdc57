<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Validator\Model\Subject;

use LightSaml\Model\Assertion\Subject;

interface SubjectValidatorInterface
{
    /**
     * @return void
     */
    public function validateSubject(Subject $subject);
}
