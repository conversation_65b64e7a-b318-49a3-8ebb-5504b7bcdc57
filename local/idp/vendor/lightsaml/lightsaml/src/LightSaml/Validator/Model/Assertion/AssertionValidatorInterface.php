<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Validator\Model\Assertion;

use LightSaml\Model\Assertion\Assertion;

interface AssertionValidatorInterface
{
    /**
     * @throws \LightSaml\Error\LightSamlValidationException
     *
     * @return void
     */
    public function validateAssertion(Assertion $assertion);
}
