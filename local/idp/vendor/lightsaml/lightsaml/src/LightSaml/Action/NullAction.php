<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Action;

use LightSaml\Context\ContextInterface;

class NullAction implements ActionInterface
{
    /**
     * @return void
     */
    public function execute(ContextInterface $context)
    {
        // null
    }
}
