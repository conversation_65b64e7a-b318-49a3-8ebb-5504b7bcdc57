<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Action;

use Psr\Log\LoggerInterface;

class ActionLogWrapper implements ActionWrapperInterface
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @return ActionInterface
     */
    public function wrap(ActionInterface $action)
    {
        return new LoggableAction($action, $this->logger);
    }
}
