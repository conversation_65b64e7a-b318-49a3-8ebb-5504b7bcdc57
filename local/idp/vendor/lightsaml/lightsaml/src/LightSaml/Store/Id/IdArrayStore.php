<?php

/*
 * This file is part of the LightSAML-Core package.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace LightSaml\Store\Id;

class IdArrayStore implements IdStoreInterface
{
    /** @var array */
    protected $store = [];

    /**
     * @param string $entityId
     * @param string $id
     *
     * @return void
     */
    public function set($entityId, $id, \DateTime $expiryTime)
    {
        if (false == isset($this->store[$entityId])) {
            $this->store[$entityId] = [];
        }
        $this->store[$entityId][$id] = $expiryTime;
    }

    /**
     * @param string $entityId
     * @param string $id
     *
     * @return bool
     */
    public function has($entityId, $id)
    {
        return isset($this->store[$entityId][$id]);
    }
}
