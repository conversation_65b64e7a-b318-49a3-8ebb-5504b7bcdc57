mode: 'usage' # usage | call | inheritance
source: './src'
filePattern: '*.php'
ignore: ['Tests', 'Builder']
formatter: 'Php<PERSON>\Writer\Strategy\Json'
target: './phpda.json'
# referenceValidator: 'Fully\Qualified\Class\Name\To\ReferenceValidator'
groupLength: 1
visitor:
  - PhpDA\Parser\Visitor\TagCollector
  - PhpDA\Parser\Visitor\SuperglobalCollector
#  - PhpDA\Parser\Visitor\UnsupportedEvalCollector
#  - PhpDA\Parser\Visitor\UnsupportedFuncCollector
#  - PhpDA\Parser\Visitor\UnsupportedVarCollector
#  - PhpDA\Parser\Visitor\UnsupportedGlobalCollector
#  - PhpDA\Parser\Visitor\NamespacedStringCollector
#  - PhpDA\Parser\Visitor\IocContainerAccessorCollector
visitorOptions:
  PhpDA\Parser\Visitor\Required\DeclaredNamespaceCollector: {minDepth: 2, sliceLength: 3}
  PhpDA\Parser\Visitor\Required\MetaNamespaceCollector: {minDepth: 2, sliceLength: 3}
  PhpDA\Parser\Visitor\Required\UsedNamespaceCollector: {minDepth: 2, sliceLength: 3}
  PhpDA\Parser\Visitor\TagCollector: {minDepth: 2, sliceLength: 3}
