{"name": "lightsaml/lightsaml", "license": "MIT", "type": "library", "description": "Light SAML 2.0 PHP library", "keywords": ["SAML 2.0", "PHP", "library", "lightSAML", "Single SignOn", "Single Logout"], "homepage": "https://www.lightsaml.com/", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/tmilos/", "role": "Developer"}], "support": {"issues": "https://github.com/lightsaml/lightsaml/issues", "source": "https://github.com/lightSAML/lightSAML", "docs": "http://www.lightsaml.com/LightSAML-Core/"}, "autoload": {"psr-0": {"LightSaml\\": "src/"}}, "autoload-dev": {"psr-0": {"LightSaml\\Tests\\": "tests/"}}, "require": {"php": ">=7.2.5", "robrichards/xmlseclibs": "~2.0|~3.0|~4.0", "symfony/http-foundation": "~5.0|~6.0", "symfony/event-dispatcher": "~5.0|~6.0"}, "require-dev": {"symfony/dom-crawler": "~5.0", "symfony/css-selector": "~5.0", "pimple/pimple": "~3.0", "phpunit/phpunit": "~8.4|~9.5", "monolog/monolog": "^2.0.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"lightsaml/symfony-bridge": "Symfony 2 build container bridge", "lightsaml/sp-bundle": "Symfony 2 SP security bundle"}, "prefer-stable": true, "minimum-stability": "stable"}