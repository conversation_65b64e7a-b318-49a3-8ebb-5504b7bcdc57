<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-develop',
        'version' => 'dev-develop',
        'reference' => '5badb3e2ed3b942e4e30080c7abf17de59c7e5e7',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => '5badb3e2ed3b942e4e30080c7abf17de59c7e5e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lightsaml/lightsaml' => array(
            'pretty_version' => '2.3.4',
            'version' => '2.3.4.0',
            'reference' => 'c803d864c650dc8948078b04f00928f53edffbd7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lightsaml/lightsaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'litesaml/lightsaml' => array(
            'pretty_version' => '4.5.0',
            'version' => '4.5.0.0',
            'reference' => '144f1c7d99d22bb2f69ae55e57f238a7b87884cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../litesaml/lightsaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'litesaml/schemas' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '11f0dec57cd55f7fd2378bf9f55ae6c7c38d15b7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../litesaml/schemas',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'robrichards/xmlseclibs' => array(
            'pretty_version' => '3.1.3',
            'version' => '3.1.3.0',
            'reference' => '2bdfd742624d739dfadbd415f00181b4a77aaf07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../robrichards/xmlseclibs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
