<?php

namespace tool_imgoptimizer\traits;

use stored_file;
trait stored_file_manipulation_trait {

    public function replace_stored_file_content(stored_file $file, string $content, array $overrides = []){
        $fileinfo = (object)[
            'contextid'   => $file->get_contextid(),
            'component'   => $file->get_component(),
            'filearea'    => $file->get_filearea() . uniqid('_temp_'),
            'itemid'      => $file->get_itemid(),
            'filepath'    => $file->get_filepath(),
            'filename'    => $file->get_filename(),
            'timecreated' => $file->get_timecreated(),
            'userid'      => $file->get_userid(),
            'mimetype'    => $file->get_mimetype(),
        ];

        foreach ($overrides as $key => $value) {
            $fileinfo->$key = $value;
        }

        try {
            $fs = get_file_storage();
            $temp_file = $fs->create_file_from_string($fileinfo, $content);
            $file->replace_file_with($temp_file);
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER);
        } finally {
            $fs->delete_area_files($fileinfo->contextid, $fileinfo->component, $fileinfo->filearea);
        }
    }

    public function replace_stored_file_content_from_path(stored_file $file, string $path, array $overrides = []){
        $fileinfo = (object)[
            'contextid'   => $file->get_contextid(),
            'component'   => $file->get_component(),
            'filearea'    => $file->get_filearea() . uniqid('_temp_'),
            'itemid'      => $file->get_itemid(),
            'filepath'    => $file->get_filepath(),
            'filename'    => $file->get_filename(),
            'timecreated' => $file->get_timecreated(),
            'userid'      => $file->get_userid(),
            'mimetype'    => $file->get_mimetype(),
        ];

        foreach ($overrides as $key => $value) {
            $fileinfo->$key = $value;
        }

        try {
            $fs = get_file_storage();
            $temp_file = $fs->create_file_from_pathname($fileinfo, $path);
            $file->replace_file_with($temp_file);
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER);
        } finally {
            $fs->delete_area_files($fileinfo->contextid, $fileinfo->component, $fileinfo->filearea);
        }
    }
}