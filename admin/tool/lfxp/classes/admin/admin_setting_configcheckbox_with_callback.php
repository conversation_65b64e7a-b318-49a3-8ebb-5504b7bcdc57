<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace tool_lfxp\admin;

use admin_setting_configcheckbox;


/**
 * Classe personalizada para admin_setting_configcheckbox com callback.
 *
 * @package    tool_lfxp
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class admin_setting_configcheckbox_with_callback extends \admin_setting_configcheckbox
{
    /** @var callable|null Função callback a ser executada após salvar a configuração. */
    protected $callback;

    /**
     * Construtor.
     *
     * @param string $name Nome da configuração.
     * @param string $visiblename Nome visível da configuração.
     * @param string $description Descrição da configuração.
     * @param int $defaultvalue Valor padrão.
     * @param callable|null $callback Função callback a ser executada após salvar a configuração.
     */
    public function __construct($name, $visiblename, $description, $defaultvalue = 0, $callback = null)
    {
        parent::__construct($name, $visiblename, $description, $defaultvalue);
        $this->callback = $callback;
    }

    /**
     * Sobrescreve o método write_setting para executar o callback após salvar a configuração.
     *
     * @param mixed $data Dados enviados pelo formulário.
     * @return string Mensagem de status.
     */
    public function write_setting($data)
    {
        // Salva a configuração normalmente.
        $result = parent::write_setting($data);

        // Executa o callback, se definido.
        if ($this->callback && is_callable($this->callback)) {
            call_user_func($this->callback, $data);
        }

        return $result;
    }
}


