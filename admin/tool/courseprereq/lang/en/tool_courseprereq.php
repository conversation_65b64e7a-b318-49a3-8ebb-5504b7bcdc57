<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Course Prerequisite Manager
 *
 * @package    tool_courseprereq
 * @category   string
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Course Prerequisite Manager';
$string['privacy:metadata'] = 'The Course Prerequisite Manager plugin doesn\'t store any personal data.';
$string['error_course_self_prerequisite'] = 'A course cannot be a prerequisite of itself';
$string['courseprereq:manage'] = 'Manage course prerequisites';

//events
$string['event:tool_courseprereq_added'] = 'Course prerequisite added';
$string['event:tool_courseprereq_removed'] = 'Course prerequisite removed';

//task
$string['task:updatetoolcourseprereqtable'] = 'Update tool_courseprereq table';

$string['settings'] = 'Course Prerequisite Manager Settings';