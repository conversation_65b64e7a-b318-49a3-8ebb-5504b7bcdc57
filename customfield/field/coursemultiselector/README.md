# Course Multiselector Custom Field

Um campo personalizado do Moodle que permite seleção múltipla de cursos com tags coloridas baseadas no status do curso.

## Características

- **Seleção múltipla de cursos**: Interface de autocomplete para seleção de múltiplos cursos
- **Tags coloridas por status**: As tags dos cursos são coloridas conforme o status:
  - **Cinza**: Cursos ocultos (hidden)
  - **Verde**: Cursos futuros (future) - ainda não iniciados
  - **Rosa/Vermelho**: Cursos expirados (expired) - já finalizados
  - **Azul**: Cursos ativos (active)
- **Legenda explicativa**: Legenda visual que explica o significado das cores
- **Filtros configuráveis**: Permite configurar quais tipos de curso mostrar
- **Responsivo**: Interface adaptável para dispositivos móveis

## Instalação

1. Copie o diretório `coursemultiselector` para `customfield/field/`
2. Acesse a administração do Moodle e complete a instalação
3. O campo estará disponível para uso em campos personalizados

## Configuração

### Configurações do Campo

- **Mostrar cursos ocultos**: Inclui cursos ocultos na seleção
- **Mostrar cursos futuros**: Inclui cursos com data de início no futuro
- **Mostrar cursos expirados**: Inclui cursos com data de fim no passado

### Como Usar

1. Vá para **Administração do site > Usuários > Campos de perfil do usuário**
2. Clique em **Criar um novo campo de perfil**
3. Selecione **Seletor Múltiplo de Cursos**
4. Configure as opções desejadas
5. Salve o campo

## Estrutura de Arquivos

```
coursemultiselector/
├── classes/
│   ├── field_controller.php      # Controlador principal do campo
│   ├── data_controller.php       # Controlador de dados
│   ├── form/
│   │   └── course_autocomplete.php # Elemento de formulário personalizado
│   ├── event/                     # Eventos para auditoria
│   └── privacy/                   # Implementação de privacidade
├── lang/
│   ├── en/                        # Strings em inglês
│   └── pt_br/                     # Strings em português brasileiro
├── templates/
│   ├── display.mustache           # Template para exibição
│   └── course_autocomplete_tags.mustache # Template para tags
├── amd/
│   ├── src/
│   │   └── course_autocomplete.js # JavaScript AMD
│   └── build/
│       └── course_autocomplete.min.js # JavaScript minificado
├── styles.css                     # Estilos CSS
├── version.php                    # Informações da versão
└── README.md                      # Esta documentação
```

## Desenvolvimento

### JavaScript

O campo usa um módulo AMD JavaScript (`course_autocomplete.js`) que:
- Monitora mudanças no autocomplete
- Aplica classes CSS baseadas no status do curso
- Adiciona tooltips informativos
- Usa MutationObserver para detectar mudanças dinâmicas

### CSS

O arquivo `styles.css` define:
- Cores específicas para cada status de curso
- Estilos responsivos
- Efeitos de hover
- Estilos para a legenda

### Templates Mustache

- `display.mustache`: Renderiza a exibição final do campo
- `course_autocomplete_tags.mustache`: Template para tags individuais

## Status dos Cursos

O sistema determina o status do curso baseado em:

1. **Hidden (Oculto)**: `course.visible = 0`
2. **Future (Futuro)**: `course.startdate > now()`
3. **Expired (Expirado)**: `course.enddate < now()`
4. **Active (Ativo)**: Todos os outros casos

## Personalização

### Cores

Para alterar as cores, edite o arquivo `styles.css`:

```css
.coursemultiselector-hidden { background-color: #6c757d; }
.coursemultiselector-future { background-color: #28a745; }
.coursemultiselector-expired { background-color: #e83e8c; }
.coursemultiselector-active { background-color: #007bff; }
```

### Strings de Idioma

Adicione ou modifique strings nos arquivos de idioma em `lang/`.

## Teste

Execute o arquivo de teste em:
`/customfield/field/coursemultiselector/test_field.php`

## Suporte

Para suporte técnico, entre em contato com REVVO <www.revvo.com.br>

## Licença

GPL v3 ou posterior - mesma licença do Moodle
