{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template customfield_coursemultiselector/display

    Course  field display template

    Context variables required for this template:
    * courses - Array of course objects with name, shortname, and status
    * fieldname - Name of the field
    * helptext - Help text for status colors

    Example context (json):
    {
        "courses": [
            {
                "name": "Introduction to Programming",
                "shortname": "PROG101",
                "status": "active"
            },
            {
                "name": "Mathematics Basics",
                "shortname": "MATH101", 
                "status": "future"
            }
        ],
        "fieldname": "Course ",
        "helptext": "Course tags are color-coded: <PERSON> (hidden), <PERSON> (future), <PERSON> (expired), <PERSON> (active)."
    }
}}
{{#courses.0}}
<div class="coursemultiselector-tags-container">
    {{#courses}}
    <span class="coursemultiselector-tag coursemultiselector-{{status}}"
          title="{{#str}}status_{{status}}, customfield_coursemultiselector{{/str}}">
        {{name}} ({{shortname}})
    </span>
    {{/courses}}
</div>
{{#helptext}}
<div class="coursemultiselector-help">
    <small>{{helptext}}</small>
</div>
{{/helptext}}
{{/courses.0}}
{{^courses.0}}
<div class="coursemultiselector-empty">
    <em>{{#str}}nocoursesfound, customfield_coursemultiselector{{/str}}</em>
</div>
{{/courses.0}}
