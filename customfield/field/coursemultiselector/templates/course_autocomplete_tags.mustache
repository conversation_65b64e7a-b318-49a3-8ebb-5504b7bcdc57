{{!
    @template customfield_coursemultiselector/course_autocomplete_tags

    Template for rendering course autocomplete tags with status-based colors.

    Classes required for JS:
    * coursemultiselector-tag
    * coursemultiselector-{status}

    Data attributes required for JS:
    * data-value - Course ID
    * data-status - Course status

    Context variables required for this template:
    * courses - Array of course objects with id, name, status
    * selected - Array of selected course IDs

    Example context (json):
    {
        "courses": [
            {
                "id": "1",
                "name": "Course Name",
                "status": "active",
                "selected": true
            }
        ]
    }
}}

<div class="coursemultiselector-tags-container">
    {{#courses}}
        {{#selected}}
            <span class="coursemultiselector-tag coursemultiselector-{{status}}" 
                  data-value="{{id}}" 
                  data-status="{{status}}"
                  title="{{name}} ({{status_text}})">
                {{name}}
                <button type="button" class="btn-close" aria-label="{{#str}}remove, core{{/str}}" data-action="remove-tag">
                    <span aria-hidden="true">&times;</span>
                </button>
            </span>
        {{/selected}}
    {{/courses}}
</div>
