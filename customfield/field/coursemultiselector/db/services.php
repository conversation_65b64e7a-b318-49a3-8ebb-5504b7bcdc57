<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Web service definitions for coursemultiselector custom field
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$functions = [
    'customfield_coursemultiselector_search_courses' => [
        'classname' => 'customfield_coursemultiselector\external\search_courses',
        'methodname' => 'execute',
        'description' => 'Search courses for autocomplete field',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'moodle/course:view',
        'loginrequired' => true,
    ],
];
