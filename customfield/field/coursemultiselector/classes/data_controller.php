<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_coursemultiselector;

/**
 * Manages field data
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class data_controller extends \core_customfield\data_controller {
    public function datafield() : string {
        return 'value';
    }

    public function instance_form_save($value) {
        $fieldname = $this->get_form_element_name();
        $value = isset($value->{$fieldname}) ? $value->{$fieldname} : "";

        if (is_array($value)) {
            $value = implode(',', $value);
        }

        $oldvalue = $this->get('value');
        $this->data->set('value', $value);
        //$this->data->set('valueformat', FORMAT_MOODLE);
        $this->save();

        // Log the event for audit purposes
        if ($oldvalue !== $value) {
            $event = \customfield_coursemultiselector\event\data_updated::create([
                'objectid' => $this->get('id'),
                'context' => $this->get_context(),
                'other' => [
                    'instanceid' => $this->get('instanceid'),
                    'fieldid' => $this->get('fieldid'),
                    'oldvalue' => $oldvalue,
                    'newvalue' => $value
                ]
            ]);
            $event->trigger();
        }
    }
    
    public function instance_form_before_set_data($instance) {
        $value = $this->get('value');
        $fieldname = $this->get_form_element_name();
        
        if (!empty($value)) {
            $instance->{$fieldname} = explode(',', $value);
        } else {
            $instance->{$fieldname} = [];
        }
    }

    public function instance_form_definition(\MoodleQuickForm $mform) {
        $field = $this->get_field();
        $field->field_definition($mform);        
    }

    public function instance_form_validation(array $data, array $files) : array {
        global $DB;

        $errors = parent::instance_form_validation($data, $files);
        $fieldname = $this->get_form_element_name();

        if (isset($data[$fieldname]) && !empty($data[$fieldname])) {
            $courseids = is_array($data[$fieldname]) ? $data[$fieldname] : explode(',', $data[$fieldname]);

            foreach ($courseids as $courseid) {
                if (!is_numeric($courseid)) {
                    $errors[$fieldname] = get_string('error_invalidcourse', 'customfield_coursemultiselector', $courseid);
                    continue;
                }

                // Check if course exists
                if (!$DB->record_exists('course', ['id' => $courseid])) {
                    $errors[$fieldname] = get_string('error_coursenotfound', 'customfield_coursemultiselector', $courseid);
                    continue;
                }

                // Check if user has permission to view the course
                $context = \context_course::instance($courseid);
                if (!has_capability('moodle/course:view', $context)) {
                    $course = $DB->get_record('course', ['id' => $courseid], 'fullname');
                    $errors[$fieldname] = get_string('error_nopermission', 'customfield_coursemultiselector',
                        $course ? $course->fullname : $courseid);
                }
            }
        }

        return $errors;
    }

    public function get_default_value() {
        return $this->get_field()->get_configdata_property('defaultvalue');
    }

    public function export_value() {
        return $this->get('value');
    }

    /**
     * Returns the value formatted for display
     *
     * @return string
     */
    public function display() : string {
        global $DB, $PAGE;

        $value = $this->get('value');
        if (empty($value)) {
            return '';
        }

        $courseids = explode(',', $value);
        if (empty($courseids)) {
            return '';
        }

        // Get course information
        list($insql, $params) = $DB->get_in_or_equal($courseids, SQL_PARAMS_NAMED);
        $sql = "SELECT id, fullname, shortname, visible, startdate, enddate
                FROM {course}
                WHERE id $insql";

        $courses = $DB->get_records_sql($sql, $params);

        if (empty($courses)) {
            return '';
        }

        // Apply field configuration filters for display
        $field = $this->get_field();
        $configdata = $field->get('configdata');
        $filterhidden = !empty($configdata['filterhidden']);
        $filterfuture = !empty($configdata['filterfuture']);
        $filterexpired = !empty($configdata['filterexpired']);

        $displaycourses = [];
        $now = time();

        foreach ($courses as $course) {
            $status = $field->get_course_status($course);

            // Apply display filters based on configuration
            if (!$filterhidden && $status === 'hidden') {
                continue;
            }
            if (!$filterfuture && $status === 'future') {
                continue;
            }
            if (!$filterexpired && $status === 'expired') {
                continue;
            }

            $displaycourses[] = [
                'name' => format_string($course->fullname),
                'shortname' => format_string($course->shortname),
                'status' => $status
            ];
        }

        if (empty($displaycourses)) {
            return '';
        }

        // Generate HTML with status tags
        $templatecontext = [
            'courses' => $displaycourses,
            'fieldname' => $this->get_field()->get_formatted_name(),
            'helptext' => get_string('help_statuscolors', 'customfield_coursemultiselector')
        ];

        // Add CSS if not already added
        $PAGE->requires->css('/customfield/field/coursemultiselector/styles.css');

        $output = $PAGE->get_renderer('core');
        return $output->render_from_template('customfield_coursemultiselector/display', $templatecontext);
    }

    /**
     * Returns the value as it would be stored in the database (not in human-readable format).
     *
     * @return mixed
     */
    public function get_value() {
        if (!$this->get('id')) {
            return $this->get_default_value();
        }
        return $this->get($this->datafield());
    }
}