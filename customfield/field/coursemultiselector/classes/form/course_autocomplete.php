<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_coursemultiselector\form;

use MoodleQuickForm_autocomplete;

/**
 * Custom autocomplete form element for course selection with status-based coloring
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class course_autocomplete extends MoodleQuickForm_autocomplete {

    /**
     * Course data with status information
     * @var array
     */
    protected $coursedata = [];

    /**
     * Constructor
     *
     * @param string $elementName Element name
     * @param mixed $elementLabel Element label
     * @param array $options Options array
     * @param array $attributes Element attributes
     */
    public function __construct($elementName = null, $elementLabel = null, $options = null, $attributes = null) {
        parent::__construct($elementName, $elementLabel, $options, $attributes);

        $this->_type = 'course_autocomplete';
    }

    /**
     * Returns HTML for the autocomplete element with custom styling
     *
     * @return string HTML output
     */
    public function toHtml() {
        global $PAGE;

        // Add custom JavaScript for tag styling
        $this->add_custom_javascript();

        // Get the standard HTML
        $html = parent::toHtml();

        // Add status legend
        $legend = $this->get_status_legend();

        return $html . $legend;
    }

    /**
     * Add custom JavaScript for styling tags based on course status
     */
    protected function add_custom_javascript() {
        global $PAGE;

        $elementid = $this->getAttribute('id');

        // Use AMD module for better organization
        $PAGE->requires->js_call_amd(
            'customfield_coursemultiselector/course_autocomplete',
            'init',
            [$elementid, $this->coursedata]
        );
    }

    /**
     * Generate status legend HTML
     *
     * @return string Legend HTML
     */
    protected function get_status_legend(): string {
        $legend = '<div class="coursemultiselector-legend mt-2">';
        $legend .= '<small class="text-muted">';
        $legend .= get_string('help_statuscolors', 'customfield_coursemultiselector') . '<br>';

        $legend .= '<span class="coursemultiselector-tag coursemultiselector-hidden mr-1">' .
                   get_string('status_hidden', 'customfield_coursemultiselector') . '</span>';

        $legend .= '<span class="coursemultiselector-tag coursemultiselector-future mr-1">' .
                   get_string('status_future', 'customfield_coursemultiselector') . '</span>';

        $legend .= '<span class="coursemultiselector-tag coursemultiselector-expired mr-1">' .
                   get_string('status_expired', 'customfield_coursemultiselector') . '</span>';

        $legend .= '<span class="coursemultiselector-tag coursemultiselector-active mr-1">' .
                   get_string('status_active', 'customfield_coursemultiselector') . '</span>';

        $legend .= '</small>';
        $legend .= '</div>';

        return $legend;
    }
}
