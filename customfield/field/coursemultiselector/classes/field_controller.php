<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_coursemultiselector;

use MoodleQuickForm;

MoodleQuickForm::registerElementType(
    'course_autocomplete',
    $CFG->dirroot . '/customfield/field/coursemultiselector/classes/form/course_autocomplete.php',
    '\\customfield_coursemultiselector\\form\\course_autocomplete'
);

/**
 * Manages field information (without data)
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class field_controller extends \core_customfield\field_controller
{
    /**
     * Plugin type
     */
    const TYPE = 'coursemultiselector';

    /**
     * Defines the configuration form elements for the field.
     *
     * @param \MoodleQuickForm $mform The Moodle form object
     */
    public function config_form_definition(\MoodleQuickForm $mform) {
        $mform->addElement('header', 'header_specificsettings', get_string('specificsettings', 'customfield_coursemultiselector'));
        $mform->setExpanded('header_specificsettings', true);

        // Filter options for course selection
        $mform->addElement('advcheckbox', 'configdata[filterhidden]',
            get_string('filterhidden', 'customfield_coursemultiselector'),
            get_string('filterhidden_help', 'customfield_coursemultiselector'));
        $mform->setDefault('configdata[filterhidden]', 0);
        $mform->setType('configdata[filterhidden]', PARAM_BOOL);

        $mform->addElement('advcheckbox', 'configdata[filterfuture]',
            get_string('filterfuture', 'customfield_coursemultiselector'),
            get_string('filterfuture_help', 'customfield_coursemultiselector'));
        $mform->setDefault('configdata[filterfuture]', 1);
        $mform->setType('configdata[filterfuture]', PARAM_BOOL);

        $mform->addElement('advcheckbox', 'configdata[filterexpired]',
            get_string('filterexpired', 'customfield_coursemultiselector'),
            get_string('filterexpired_help', 'customfield_coursemultiselector'));
        $mform->setDefault('configdata[filterexpired]', 1);
        $mform->setType('configdata[filterexpired]', PARAM_BOOL);
    }

    /**
     * Defines the field form elements.
     *
     * Adds an autocomplete element to the form with multiple selection enabled.
     *
     * @param \MoodleQuickForm $mform The Moodle form object
     */
    public function field_definition(\MoodleQuickForm $mform)
    {
        $fieldname = "customfield_" . $this->get('shortname');

        $element = $mform->addElement(
            'course_autocomplete',
            $fieldname,
            $this->get_formatted_name(),
            [], // Empty options array for AJAX
            [
                'multiple' => true,
                'noselectionstring' => get_string('selectcourses', 'customfield_coursemultiselector'),
                'placeholder' => get_string('searchcourses', 'customfield_coursemultiselector'),
                'class' => 'coursemultiselector-field-container',
                'ajax' => 'customfield_coursemultiselector/course_autocomplete',
                'data-field-id' => $this->get('id'),
                'valuehtmlcallback' => function($value) {
                    return $this->format_course_value_for_display($value);
                }
            ]
        );

        // Pass course status data to the element for initial values
        if (method_exists($element, 'setCourseStatusData')) {
            $coursesdata = $this->get_courses_with_status_data();
            $element->setCourseStatusData($coursesdata);
        }

        $mform->setType($fieldname, PARAM_SEQUENCE);
    }

    /**
     * Get courses data with status information for enhanced autocomplete
     *
     * @return array Array of courses with detailed status information
     */
    public function get_courses_with_status_data(): array {
        global $DB;

        $configdata = $this->get('configdata');
        $filterhidden = !empty($configdata['filterhidden']);
        $filterfuture = !empty($configdata['filterfuture']);
        $filterexpired = !empty($configdata['filterexpired']);

        $sql = "SELECT c.id, c.fullname, c.shortname, c.visible, c.startdate, c.enddate
                FROM {course} c
                WHERE c.id != :siteid";

        $params = ['siteid' => SITEID];
        $conditions = [];

        // Apply visibility filter
        if (!$filterhidden) {
            $conditions[] = "c.visible = 1";
        }

        // Apply date filters
        $now = time();
        if (!$filterfuture) {
            $conditions[] = "(c.startdate = 0 OR c.startdate <= :now1)";
            $params['now1'] = $now;
        }

        if (!$filterexpired) {
            $conditions[] = "(c.enddate = 0 OR c.enddate >= :now2)";
            $params['now2'] = $now;
        }

        if (!empty($conditions)) {
            $sql .= " AND " . implode(" AND ", $conditions);
        }

        $sql .= " ORDER BY c.fullname";

        $courses = $DB->get_records_sql($sql, $params);

        $coursedata = [];
        foreach ($courses as $course) {
            $status = $this->get_course_status($course);
            $coursedata[$course->id] = [
                'id' => $course->id,
                'fullname' => format_string($course->fullname),
                'shortname' => format_string($course->shortname),
                'status' => $status,
                'visible' => $course->visible,
                'startdate' => $course->startdate,
                'enddate' => $course->enddate,
                'display_name' => format_string($course->fullname) . ' (' . format_string($course->shortname) . ')'
            ];
        }

        return $coursedata;
    }

    /**
     * Get course status for display
     *
     * @param object $course Course object
     * @return string Status identifier (hidden, future, expired, active)
     */
    public function get_course_status($course): string {
        $now = time();

        if (!$course->visible) {
            return 'hidden';
        }

        if ($course->startdate > 0 && $course->startdate > $now) {
            return 'future';
        }

        if ($course->enddate > 0 && $course->enddate < $now) {
            return 'expired';
        }

        return 'active';
    }

    /**
     * Format course value for display in autocomplete
     *
     * @param int $value Course ID
     * @return string|false Formatted HTML or false if course not found
     */
    public function format_course_value_for_display($value) {
        global $DB;

        $courseid = (int)$value;
        if (!$courseid) {
            return false;
        }

        $course = $DB->get_record('course', ['id' => $courseid], 'id, fullname, shortname, visible, startdate, enddate', IGNORE_MISSING);
        if (!$course) {
            return false;
        }

        $status = $this->get_course_status($course);
        $displayname = format_string($course->fullname) . ' (' . format_string($course->shortname) . ')';

        // Return HTML with status class for styling
        return '<span class="coursemultiselector-tag coursemultiselector-' . $status . '">' .
               htmlspecialchars($displayname) . '</span>';
    }

    /**
     * Validates the configuration form data.
     *
     * @param array $data The submitted form data
     * @param array $files The uploaded files (if any)
     * @return array An array of errors, if any
     */
    public function config_form_validation(array $data, $files = array()): array
    {
        $errors = parent::config_form_validation($data, $files);
        $configdata = $data['configdata'];

        // Validate filter settings
        if (isset($configdata['filterhidden']) && !is_bool($configdata['filterhidden']) &&
            !in_array($configdata['filterhidden'], [0, 1, '0', '1'])) {
            $errors['configdata[filterhidden]'] = get_string('error_invalidvalue', 'customfield_coursemultiselector');
        }

        if (isset($configdata['filterfuture']) && !is_bool($configdata['filterfuture']) &&
            !in_array($configdata['filterfuture'], [0, 1, '0', '1'])) {
            $errors['configdata[filterfuture]'] = get_string('error_invalidvalue', 'customfield_coursemultiselector');
        }

        if (isset($configdata['filterexpired']) && !is_bool($configdata['filterexpired']) &&
            !in_array($configdata['filterexpired'], [0, 1, '0', '1'])) {
            $errors['configdata[filterexpired]'] = get_string('error_invalidvalue', 'customfield_coursemultiselector');
        }

        return $errors;
    }

    /**
     * Prepare the field data to set in the configuration form
     *
     * @param \stdClass $formdata
     */
    public function prepare_for_config_form(\stdClass $formdata) {
        $configdata = $this->get('configdata');

        if (!empty($configdata)) {
            foreach (['filterhidden', 'filterfuture', 'filterexpired'] as $setting) {
                if (isset($configdata[$setting])) {
                    $formdata->{'configdata[' . $setting . ']'} = $configdata[$setting];
                }
            }
        }
    }
}
