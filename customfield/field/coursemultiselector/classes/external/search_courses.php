<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace customfield_coursemultiselector\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use core_external\external_value;
use context_system;
use customfield_coursemultiselector\field_controller;

/**
 * External API for searching courses
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class search_courses extends external_api {

    /**
     * Returns description of method parameters
     *
     * @return external_function_parameters
     */
    public static function execute_parameters(): external_function_parameters {
        return new external_function_parameters([
            'query' => new external_value(PARAM_TEXT, 'Search query'),
            'fieldid' => new external_value(PARAM_INT, 'Field ID for configuration', VALUE_DEFAULT, 0),
        ]);
    }

    /**
     * Search courses based on query
     *
     * @param string $query Search query
     * @param int $fieldid Field ID for configuration
     * @return array
     */
    public static function execute(string $query, int $fieldid = 0): array {
        global $DB;

        // Validate parameters
        $params = self::validate_parameters(self::execute_parameters(), [
            'query' => $query,
            'fieldid' => $fieldid
        ]);

        // Check permissions
        $context = context_system::instance();
        self::validate_context($context);
        require_capability('moodle/course:view', $context);

        $query = trim($params['query']);
        $fieldid = $params['fieldid'];

        // Get field configuration if field ID is provided
        $configdata = [];
        if ($fieldid > 0) {
            $field = $DB->get_record('customfield_field', ['id' => $fieldid], '*', IGNORE_MISSING);
            if ($field && $field->configdata) {
                $configdata = json_decode($field->configdata, true) ?: [];
            }
        }

        // Build search conditions
        $conditions = [];
        $params_sql = [];

        // Basic search conditions
        if (!empty($query)) {
            $conditions[] = "(c.fullname " . $DB->sql_like('c.fullname', ':query1', false) . 
                           " OR c.shortname " . $DB->sql_like('c.shortname', ':query2', false) . ")";
            $params_sql['query1'] = '%' . $DB->sql_like_escape($query) . '%';
            $params_sql['query2'] = '%' . $DB->sql_like_escape($query) . '%';
        }

        // Exclude site course
        $conditions[] = "c.id != :siteid";
        $params_sql['siteid'] = SITEID;

        // Apply field configuration filters
        $filterhidden = !empty($configdata['filterhidden']);
        $filterfuture = !empty($configdata['filterfuture']);
        $filterexpired = !empty($configdata['filterexpired']);

        // Apply visibility filter
        if (!$filterhidden) {
            $conditions[] = "c.visible = 1";
        }

        // Apply date filters
        $now = time();
        if (!$filterfuture) {
            $conditions[] = "(c.startdate = 0 OR c.startdate <= :now1)";
            $params_sql['now1'] = $now;
        }

        if (!$filterexpired) {
            $conditions[] = "(c.enddate = 0 OR c.enddate >= :now2)";
            $params_sql['now2'] = $now;
        }

        // Build SQL query
        $sql = "SELECT c.id, c.fullname, c.shortname, c.visible, c.startdate, c.enddate
                FROM {course} c";

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " ORDER BY c.fullname";
        $sql .= " LIMIT 50"; // Limit results for performance

        $courses = $DB->get_records_sql($sql, $params_sql);

        // Process results
        $results = [];
        foreach ($courses as $course) {
            $status = self::get_course_status($course);
            $results[] = [
                'id' => (int)$course->id,
                'fullname' => format_string($course->fullname),
                'shortname' => format_string($course->shortname),
                'display_name' => format_string($course->fullname) . ' (' . format_string($course->shortname) . ')',
                'status' => $status,
                'visible' => (bool)$course->visible,
                'startdate' => (int)$course->startdate,
                'enddate' => (int)$course->enddate,
            ];
        }

        return ['courses' => $results];
    }

    /**
     * Get course status for display
     *
     * @param object $course Course object
     * @return string Status identifier (hidden, future, expired, active)
     */
    private static function get_course_status($course): string {
        $now = time();

        if (!$course->visible) {
            return 'hidden';
        }

        if ($course->startdate > 0 && $course->startdate > $now) {
            return 'future';
        }

        if ($course->enddate > 0 && $course->enddate < $now) {
            return 'expired';
        }

        return 'active';
    }

    /**
     * Returns description of method result value
     *
     * @return external_single_structure
     */
    public static function execute_returns(): external_single_structure {
        return new external_single_structure([
            'courses' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'Course ID'),
                    'fullname' => new external_value(PARAM_TEXT, 'Course full name'),
                    'shortname' => new external_value(PARAM_TEXT, 'Course short name'),
                    'display_name' => new external_value(PARAM_TEXT, 'Display name for autocomplete'),
                    'status' => new external_value(PARAM_ALPHA, 'Course status'),
                    'visible' => new external_value(PARAM_BOOL, 'Course visibility'),
                    'startdate' => new external_value(PARAM_INT, 'Course start date'),
                    'enddate' => new external_value(PARAM_INT, 'Course end date'),
                ])
            )
        ]);
    }
}
