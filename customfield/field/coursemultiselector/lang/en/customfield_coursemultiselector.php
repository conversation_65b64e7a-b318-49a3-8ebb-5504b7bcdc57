<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Multiple Course  Selector
 *
 * @package    customfield_coursemultiselector
 * @category   string
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Multiple Course  Selector';
$string['privacy:metadata'] = 'The Multiple Course  Selector field type plugin doesn\'t store any personal data; it uses tables defined in core.';
$string['specificsettings'] = 'Multiple Course  Selector field settings';
$string['fieldname'] = 'Course ';
$string['fielddescription'] = 'Select multiple courses ';

// Configuration options
$string['filterhidden'] = 'Show hidden courses';
$string['filterhidden_help'] = 'When enabled, hidden courses will be available for selection and displayed in the field.';
$string['filterfuture'] = 'Show future courses';
$string['filterfuture_help'] = 'When enabled, courses with start dates in the future will be available for selection and displayed.';
$string['filterexpired'] = 'Show expired courses';
$string['filterexpired_help'] = 'When enabled, courses with end dates in the past will be available for selection and displayed.';

// Field interface
$string['selectcourses'] = 'Select courses...';
$string['searchcourses'] = 'Search for courses';
$string['nocoursesfound'] = 'No courses found matching your search criteria.';
$string['coursesselected'] = '{$a} courses selected';

// Course status labels
$string['status_hidden'] = 'Hidden course';
$string['status_future'] = 'Future course (not yet started)';
$string['status_expired'] = 'Expired course (ended)';
$string['status_active'] = 'Active course';

// Validation messages
$string['error_invalidcourse'] = 'Invalid course selected: {$a}';
$string['error_coursenotfound'] = 'Course not found: {$a}';
$string['error_nopermission'] = 'You do not have permission to select this course: {$a}';
$string['error_invalidvalue'] = 'Invalid configuration value provided.';

// Help text
$string['help_coursemultiselector'] = 'Select one or more courses that must be completed before students can enroll in this course.';
$string['help_statuscolors'] = 'Course tags are color-coded: Gray (hidden), Green (future), Pink (expired), Blue (active).';

// Audit logging
$string['eventfieldcreated'] = 'Course  field created';
$string['eventfieldupdated'] = 'Course  field updated';
$string['eventfielddeleted'] = 'Course  field deleted';
$string['eventdatacreated'] = 'Course  data created';
$string['eventdataupdated'] = 'Course  data updated';
$string['eventdatadeleted'] = 'Course  data deleted';

// Color legend
$string['legend_title'] = 'Course Status Legend';
$string['legend_description'] = 'Tag colors indicate the current course status:';

// Actions
$string['remove'] = 'Remove';
$string['add'] = 'Add';
