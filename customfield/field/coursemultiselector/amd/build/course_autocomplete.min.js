define("customfield_coursemultiselector/course_autocomplete",["exports","jquery"],(function(_exports,_jquery){var obj;
/**
   * Course autocomplete with status-based styling
   *
   * @module     customfield_coursemultiselector/course_autocomplete
   * @copyright  2025 REVVO <www.revvo.com.br>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=_exports.default=void 0,_jquery=(obj=_jquery)&&obj.__esModule?obj:{default:obj};const init=(elementId,courseData)=>{console.log(" Estamos dentro do init do course_autocomplete.js");const styleCourseTags=()=>{let container=(0,_jquery.default)("#".concat(elementId,"_container"));container.length||(container=(0,_jquery.default)("#".concat(elementId)).closest(".form-autocomplete-container")),container.find(".form-autocomplete-selection [data-value]").each((function(){const courseId=(0,_jquery.default)(this).data("value"),tag=(0,_jquery.default)(this);if(courseData[courseId]){const status=courseData[courseId].status;tag.removeClass("coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active"),tag.addClass("coursemultiselector-tag coursemultiselector-".concat(status));const statusText=getStatusText(status),originalTitle=tag.attr("title")||"";originalTitle.includes(statusText)||tag.attr("title","".concat(originalTitle," (").concat(statusText,")"))}}))},getStatusText=status=>{switch(status){case"hidden":return M.util.get_string("status_hidden","customfield_coursemultiselector");case"future":return M.util.get_string("status_future","customfield_coursemultiselector");case"expired":return M.util.get_string("status_expired","customfield_coursemultiselector");case"active":return M.util.get_string("status_active","customfield_coursemultiselector");default:return""}};(0,_jquery.default)((()=>{setTimeout(styleCourseTags,200),(()=>{const element=(0,_jquery.default)("#".concat(elementId));element.on("change",(()=>{setTimeout(styleCourseTags,100)}));const observer=new MutationObserver((mutations=>{mutations.some((mutation=>"childList"===mutation.type&&mutation.addedNodes.length>0))&&setTimeout(styleCourseTags,50)}));let container=document.getElementById("".concat(elementId,"_container"));container||(container=element.closest(".form-autocomplete-container")[0]),container&&observer.observe(container,{childList:!0,subtree:!0,attributes:!1,characterData:!1})})();let checkCount=0;const checkInterval=setInterval((()=>{styleCourseTags(),checkCount++,checkCount>=10&&clearInterval(checkInterval)}),500)}))};_exports.init=init;var _default={init:init};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=course_autocomplete.min.js.map