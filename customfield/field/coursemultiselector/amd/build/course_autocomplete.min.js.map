{"version": 3, "file": "course_autocomplete.min.js", "sources": ["../src/course_autocomplete.js"], "sourcesContent": ["/**\n * Course autocomplete with status-based styling\n *\n * @module     customfield_coursemultiselector/course_autocomplete\n * @copyright  2025 REVVO <www.revvo.com.br>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport $ from 'jquery';\n\n/**\n * Initialize course autocomplete with status styling\n *\n * @param {string} elementId The ID of the autocomplete element\n * @param {object} courseData Course data with status information\n */\nexport const init = (elementId, courseData) => {\n    \n    console.log(' Estamos dentro do init do course_autocomplete.js');\n\n    /**\n     * Style tags based on course status\n     */\n    const styleCourseTags = () => {\n        let container = $(`#${elementId}_container`);\n        if (!container.length) {\n            container = $(`#${elementId}`).closest('.form-autocomplete-container');\n        }\n        \n        container.find('.form-autocomplete-selection [data-value]').each(function() {\n            const courseId = $(this).data('value');\n            const tag = $(this);\n            \n            if (courseData[courseId]) {\n                const status = courseData[courseId].status;\n                \n                // Remove existing status classes\n                tag.removeClass('coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active');\n                \n                // Add new status class\n                tag.addClass(`coursemultiselector-tag coursemultiselector-${status}`);\n                \n                // Add title with status information\n                const statusText = getStatusText(status);\n                const originalTitle = tag.attr('title') || '';\n                if (!originalTitle.includes(statusText)) {\n                    tag.attr('title', `${originalTitle} (${statusText})`);\n                }\n            }\n        });\n    };\n\n    /**\n     * Get status text for display\n     *\n     * @param {string} status Course status\n     * @return {string} Status text\n     */\n    const getStatusText = (status) => {\n        switch (status) {\n            case 'hidden':\n                return M.util.get_string('status_hidden', 'customfield_coursemultiselector');\n            case 'future':\n                return M.util.get_string('status_future', 'customfield_coursemultiselector');\n            case 'expired':\n                return M.util.get_string('status_expired', 'customfield_coursemultiselector');\n            case 'active':\n                return M.util.get_string('status_active', 'customfield_coursemultiselector');\n            default:\n                return '';\n        }\n    };\n\n    /**\n     * Setup event listeners and observers\n     */\n    const setupEventListeners = () => {\n        const element = $(`#${elementId}`);\n        \n        // Style tags on selection change\n        element.on('change', () => {\n            setTimeout(styleCourseTags, 100);\n        });\n        \n        // Use MutationObserver to catch dynamic changes\n        const observer = new MutationObserver((mutations) => {\n            const shouldUpdate = mutations.some(mutation => \n                mutation.type === 'childList' && mutation.addedNodes.length > 0\n            );\n            \n            if (shouldUpdate) {\n                setTimeout(styleCourseTags, 50);\n            }\n        });\n        \n        let container = document.getElementById(`${elementId}_container`);\n        if (!container) {\n            container = element.closest('.form-autocomplete-container')[0];\n        }\n        \n        if (container) {\n            observer.observe(container, { \n                childList: true, \n                subtree: true,\n                attributes: false,\n                characterData: false\n            });\n        }\n    };\n\n    // Initialize when DOM is ready\n    $(() => {\n        // Initial styling\n        setTimeout(styleCourseTags, 200);\n        \n        // Setup event listeners\n        setupEventListeners();\n        \n        // Periodic check for late-loading elements\n        let checkCount = 0;\n        const checkInterval = setInterval(() => {\n            styleCourseTags();\n            checkCount++;\n            \n            if (checkCount >= 10) { // Stop after 10 attempts (5 seconds)\n                clearInterval(checkInterval);\n            }\n        }, 500);\n    });\n};\n\nexport default {\n    init\n};"], "names": ["init", "elementId", "courseData", "console", "log", "styleCourseTags", "container", "length", "closest", "find", "each", "courseId", "this", "data", "tag", "status", "removeClass", "addClass", "statusText", "getStatusText", "originalTitle", "attr", "includes", "M", "util", "get_string", "setTimeout", "element", "on", "observer", "MutationObserver", "mutations", "some", "mutation", "type", "addedNodes", "document", "getElementById", "observe", "childList", "subtree", "attributes", "characterData", "setupEventListeners", "checkCount", "checkInterval", "setInterval", "clearInterval"], "mappings": ";;;;;;;iKAgBaA,KAAO,CAACC,UAAWC,cAE5BC,QAAQC,IAAI,2DAKNC,gBAAkB,SAChBC,WAAY,8BAAML,yBACjBK,UAAUC,SACXD,WAAY,8BAAML,YAAaO,QAAQ,iCAG3CF,UAAUG,KAAK,6CAA6CC,MAAK,iBACvDC,UAAW,mBAAEC,MAAMC,KAAK,SACxBC,KAAM,mBAAEF,SAEVV,WAAWS,UAAW,OAChBI,OAASb,WAAWS,UAAUI,OAGpCD,IAAIE,YAAY,gHAGhBF,IAAIG,+DAAwDF,eAGtDG,WAAaC,cAAcJ,QAC3BK,cAAgBN,IAAIO,KAAK,UAAY,GACtCD,cAAcE,SAASJ,aACxBJ,IAAIO,KAAK,kBAAYD,2BAAkBF,sBAYjDC,cAAiBJ,gBACXA,YACC,gBACMQ,EAAEC,KAAKC,WAAW,gBAAiB,uCACzC,gBACMF,EAAEC,KAAKC,WAAW,gBAAiB,uCACzC,iBACMF,EAAEC,KAAKC,WAAW,iBAAkB,uCAC1C,gBACMF,EAAEC,KAAKC,WAAW,gBAAiB,iDAEnC,0BA0CjB,KAEEC,WAAWrB,gBAAiB,KArCJ,YAClBsB,SAAU,8BAAM1B,YAGtB0B,QAAQC,GAAG,UAAU,KACjBF,WAAWrB,gBAAiB,IAA5B,UAIEwB,SAAW,IAAIC,kBAAkBC,YACdA,UAAUC,MAAKC,UACd,cAAlBA,SAASC,MAAwBD,SAASE,WAAW5B,OAAS,KAI9DmB,WAAWrB,gBAAiB,WAIhCC,UAAY8B,SAASC,yBAAkBpC,yBACtCK,YACDA,UAAYqB,QAAQnB,QAAQ,gCAAgC,IAG5DF,WACAuB,SAASS,QAAQhC,UAAW,CACxBiC,WAAW,EACXC,SAAS,EACTC,YAAY,EACZC,eAAe,KAWvBC,OAGIC,WAAa,QACXC,cAAgBC,aAAY,KAC9BzC,kBACAuC,aAEIA,YAAc,IACdG,cAAcF,iBAEnB,IAPH,qCAWO,CACX7C"}