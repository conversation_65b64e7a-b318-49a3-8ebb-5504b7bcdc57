/**
 * Course autocomplete with AJAX support and status-based styling
 *
 * @module     customfield_coursemultiselector/course_autocomplete
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import $ from 'jquery';
import Ajax from 'core/ajax';

/**
 * Initialize course autocomplete with status styling
 *
 * @param {string} elementId The ID of the autocomplete element
 * @param {object} courseData Course data with status information
 */
export const init = (elementId, courseData) => {
    
    console.log(' Estamos dentro do init do course_autocomplete.js');

    /**
     * Style tags based on course status
     */
    const styleCourseTags = () => {
        let container = $(`#${elementId}_container`);
        if (!container.length) {
            container = $(`#${elementId}`).closest('.form-autocomplete-container');
        }
        
        container.find('.form-autocomplete-selection [data-value]').each(function() {
            const courseId = $(this).data('value');
            const tag = $(this);
            
            if (courseData[courseId]) {
                const status = courseData[courseId].status;
                
                // Remove existing status classes
                tag.removeClass('coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active');
                
                // Add new status class
                tag.addClass(`coursemultiselector-tag coursemultiselector-${status}`);
                
                // Add title with status information
                const statusText = getStatusText(status);
                const originalTitle = tag.attr('title') || '';
                if (!originalTitle.includes(statusText)) {
                    tag.attr('title', `${originalTitle} (${statusText})`);
                }
            }
        });
    };

    /**
     * Get status text for display
     *
     * @param {string} status Course status
     * @return {string} Status text
     */
    const getStatusText = (status) => {
        switch (status) {
            case 'hidden':
                return M.util.get_string('status_hidden', 'customfield_coursemultiselector');
            case 'future':
                return M.util.get_string('status_future', 'customfield_coursemultiselector');
            case 'expired':
                return M.util.get_string('status_expired', 'customfield_coursemultiselector');
            case 'active':
                return M.util.get_string('status_active', 'customfield_coursemultiselector');
            default:
                return '';
        }
    };

    /**
     * Setup event listeners and observers
     */
    const setupEventListeners = () => {
        const element = $(`#${elementId}`);
        
        // Style tags on selection change
        element.on('change', () => {
            setTimeout(styleCourseTags, 100);
        });
        
        // Use MutationObserver to catch dynamic changes
        const observer = new MutationObserver((mutations) => {
            const shouldUpdate = mutations.some(mutation => 
                mutation.type === 'childList' && mutation.addedNodes.length > 0
            );
            
            if (shouldUpdate) {
                setTimeout(styleCourseTags, 50);
            }
        });
        
        let container = document.getElementById(`${elementId}_container`);
        if (!container) {
            container = element.closest('.form-autocomplete-container')[0];
        }
        
        if (container) {
            observer.observe(container, { 
                childList: true, 
                subtree: true,
                attributes: false,
                characterData: false
            });
        }
    };

    // Initialize when DOM is ready
    $(() => {
        // Initial styling
        setTimeout(styleCourseTags, 200);
        
        // Setup event listeners
        setupEventListeners();
        
        // Periodic check for late-loading elements
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            styleCourseTags();
            checkCount++;
            
            if (checkCount >= 10) { // Stop after 10 attempts (5 seconds)
                clearInterval(checkInterval);
            }
        }, 500);
    });
};

/**
 * AJAX transport function for course search
 *
 * @param {string} selector The selector of the autocomplete element
 * @param {string} query The search query
 * @param {function} callback Success callback function
 * @param {function} failure Failure callback function
 */
export const transport = (selector, query, callback, failure) => {
    // Get field configuration from element data attributes
    const element = $(selector);
    const fieldId = element.data('field-id') || 0;

    const request = {
        methodname: 'customfield_coursemultiselector_search_courses',
        args: {
            query: query,
            fieldid: fieldId
        }
    };

    Ajax.call([request])[0]
        .then(function(response) {
            callback(response.courses || []);
        })
        .catch(function(error) {
            console.error('Course search failed:', error);
            failure(error.message || 'Search failed');
        });
};

/**
 * Process search results for display
 *
 * @param {string} selector The selector of the autocomplete element
 * @param {array} results Array of course results
 * @return {array} Processed results
 */
export const processResults = (selector, results) => {
    return results.map(function(course) {
        return {
            value: course.id,
            label: course.display_name,
            status: course.status,
            fullname: course.fullname,
            shortname: course.shortname
        };
    });
};

export default {
    init,
    transport,
    processResults
};