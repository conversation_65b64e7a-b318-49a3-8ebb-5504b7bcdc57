/**
 * CSS styles for Course  custom field
 *
 * @package    customfield_coursemultiselector
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/* Base tag styling */
.coursemultiselector-tag {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px 4px 2px 0;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    border: 1px solid transparent;
    transition: all 0.2s ease-in-out;
}

/* Status-specific colors */
.coursemultiselector-hidden {
    background-color: #6c757d;
    color: #ffffff;
    border-color: #5a6268;
}

.coursemultiselector-future {
    background-color: #28a745;
    color: #ffffff;
    border-color: #1e7e34;
}

.coursemultiselector-expired {
    background-color: #e83e8c;
    color: #ffffff;
    border-color: #d91a72;
}

.coursemultiselector-active {
    background-color: #007bff;
    color: #ffffff;
    border-color: #0056b3;
}

/* Hover effects */
.coursemultiselector-tag:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Light theme variants for better contrast */
.coursemultiselector-hidden.light {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.coursemultiselector-future.light {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.coursemultiselector-expired.light {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.coursemultiselector-active.light {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* Responsive design */
@media (max-width: 768px) {
    .coursemultiselector-tag {
        font-size: 0.8rem;
        padding: 3px 6px;
        margin: 1px 2px 1px 0;
    }
}

/* Container for tags */
.coursemultiselector-tags-container {
    margin: 4px 0;
    line-height: 1.5;
}

/* Form field styling */
.coursemultiselector-field-container {
    margin-bottom: 1rem;
}

.coursemultiselector-field-container .form-autocomplete-selection {
    min-height: 38px;
    border-radius: 4px;
}

/* Autocomplete selection styling */
.form-autocomplete-selection [data-value] {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px 4px 2px 0;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    border: 1px solid transparent;
    transition: all 0.2s ease-in-out;
}

/* Status-specific colors for autocomplete tags */
.form-autocomplete-selection [data-value].coursemultiselector-hidden {
    background-color: #6c757d !important;
    color: #ffffff !important;
    border-color: #5a6268 !important;
}

.form-autocomplete-selection [data-value].coursemultiselector-future {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border-color: #1e7e34 !important;
}

.form-autocomplete-selection [data-value].coursemultiselector-expired {
    background-color: #e83e8c !important;
    color: #ffffff !important;
    border-color: #d91a72 !important;
}

.form-autocomplete-selection [data-value].coursemultiselector-active {
    background-color: #007bff !important;
    color: #ffffff !important;
    border-color: #0056b3 !important;
}

/* Hover effects for autocomplete tags */
.form-autocomplete-selection [data-value].coursemultiselector-tag:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Configuration form styling */
.coursemultiselector-config-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin: 10px 0;
}

.coursemultiselector-config-section h4 {
    margin-top: 0;
    color: #495057;
    font-size: 1.1rem;
}

/* Help text styling */
.coursemultiselector-help {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* Legend styling */
.coursemultiselector-legend {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.coursemultiselector-legend small {
    display: block;
    line-height: 1.4;
}

.coursemultiselector-legend .coursemultiselector-tag {
    margin: 2px 4px 2px 0;
    font-size: 0.75rem;
    padding: 2px 6px;
}

/* Loading state */
.coursemultiselector-loading {
    opacity: 0.6;
    pointer-events: none;
}

.coursemultiselector-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: coursemultiselector-spin 1s linear infinite;
}

@keyframes coursemultiselector-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility improvements */
.coursemultiselector-tag:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .coursemultiselector-tag {
        background-color: transparent !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
